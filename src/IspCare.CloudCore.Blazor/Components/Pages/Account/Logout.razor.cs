using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Components;

namespace IspCare.CloudCore.Blazor.Components.Pages.Account;

public partial class Logout : ComponentBase
{
    [Inject] private IHttpContextAccessor HttpContextAccessor { get; set; } = default!;

    protected override async Task OnInitializedAsync()
    {
        var httpContext = HttpContextAccessor.HttpContext;
        if (httpContext?.User.Identity?.IsAuthenticated == true)
        {
            await httpContext.SignOutAsync();
            NavigationManager.NavigateTo("/logout", true);
        }
    }
}