using System.Security.Claims;
using IspCare.CloudCore.Application.Admin.Dtos;
using IspCare.CloudCore.Domain.AdminUsers;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Radzen;

namespace IspCare.CloudCore.Blazor.Components.Pages.Account;

public partial class Login : ComponentBase
{
    [CascadingParameter] public HttpContext? HttpContext { get; set; }
    
    public string? ErrorMessage { get; set; }

    private async Task OnLogin(LoginArgs args)
    {
        var user = await DbContext.Admins.FirstOrDefaultAsync(u => u.Email == args.Username);

        var passwordHasher = new PasswordHasher<AdminUser>();
        var result = user is not null
            ? passwordHasher.VerifyHashedPassword(user, user.PasswordHash, args.Password)
            : PasswordVerificationResult.Failed;

        if (result != PasswordVerificationResult.Success)
        {
            ErrorMessage = "Invalid username or password";
            return;
        }

        var claims = new List<Claim>
        {
            new(ClaimTypes.Email, args.Username)
        };

        var identity = new ClaimsIdentity(claims, CookieAuthenticationDefaults.AuthenticationScheme);
        var principal = new ClaimsPrincipal(identity);
        await HttpContext?.SignInAsync(principal);
        NavigationManager.NavigateTo("/");
    }
}